package vn.com.bidv.feature.government.service.constants

import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.model.BaseTransaction
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import java.util.Date

object Constants {
    val ITEM_PER_PAGE: Int = 10
    val MAX_LENGTH_DECLARATION_TEXT_INPUT: Int = 30
    val MAX_LENGTH_DEBIT_TEXT_INPUT: Int = 14
    val MAX_LENGTH_TAX_TEXT_INPUT: Int = 13
    val MAX_LENGTH_TAX_BATCH_INPUT: Int = 6
    val MAX_LENGTH_MONEY_INPUT: Int = 18
    val MAX_LENGTH_TEXT_INPUT_20: Int = 20
    val MAX_LENGTH_TEXT_INPUT_16: Int = 16
    val MAX_LENGTH_TEXT_INPUT_255: Int = 255
    val MAX_LENGTH_TEXT_INPUT_70: Int = 70
    val ADD_PAYMENT_DATA = "add.payment.data"
    val INQUIRY_TRANS_SCREEN_SHARE_DATA_KEY = "inquiry_transaction_screen_share_data_key"

    // Reload Keys
    val INQUIRY_TRANSACTION_RELOAD_KEY = ReloadKey(
        ReloadModuleKey.GOVERNMENT_SERVICE,
        ReloadFunctionKey.LIST_INQUIRY
    )

    @Deprecated("Do not use in production code")
    val fakeAddPaymentState = AddPaymentReducer.AddPaymentState(
        declarationNumber = "***********",
        date = Date(),
        chapterCodeSelected = ChapterDMO(chapterCode = "123", chapterName = "name"),
        economicCodeSelected = EconomicContentDMO(ecCode = "123", ecName = "name"),
        currencyTypeSelected = "VND",
        taxTypeSelected = TaxTypeDMO(taxTypeCode = "123", taxTypeName = "name"),
        customsCurrencySelected = CustomsCurrencyDMO(ccCode = "123", ccName = "name"),
        tradeTypeSelected = ExportImportTypeDMO(eiTypeCode = "134", eiTypeName = "name"),
        amount = "***********",
        transactionDescription = "Day la transaction description",
        fieldError = mapOf()
    )

    @Deprecated("Do not use in production code")
    val fakeTransactions = listOf<BaseTransaction>(
        object : BaseTransaction() {
            override fun getHeaderString(): String = "1234567890"
            override fun getValueTitle(): String = "Công tyădawd TNHH ABC"
            override fun getValueTaxDescription(): String =
                "Nộp thdegaefauế doanh nghiệp hàng tháng"

            override fun getValueAmount(): String = "***********"
            override fun getValueCcy(): String = "VND"
            override fun getValueDate(): Pair<String, String> = "dd/MM/yyyy" to "12/13/2024"
            override val amount = "1000000000"
            override val ccy = "VND"
            override val declarationDate = "12/15/2024"
            override val declarationNo = "1234567890"
            override val transDesc = "Nộp thuế doadnh nghiệp hàng tháng"
            override val taxTypeCode = "1"
            override val ccCode = "VND"
            override val eiTypeCode = "1"
            override val chapterCode = "1"
            override val ecCode = "1"
            override val uniqueId: String
                get() = "1"
        },
        object : BaseTransaction() {
            override fun getHeaderString(): String = "1234567890"
            override fun getValueTitle(): String = "Công ty TNHdH ABC"
            override fun getValueTaxDescription(): String = "Nộp thuế doandh nghiệp hàng tháng"
            override fun getValueAmount(): String = "1,000,0090,000 VND"
            override fun getValueCcy(): String = "VND"
            override fun getValueDate(): Pair<String, String> = "dd/MM/yyyy" to "12/19/2024"
            override val amount = "1000000000"
            override val ccy = "VND"
            override val declarationDate = "12/21/2024"
            override val declarationNo = "1234567890"
            override val transDesc = "Nộp thuế dodanh nghiệp hàng tháng"
            override val taxTypeCode = "1"
            override val ccCode = "VND"
            override val eiTypeCode = "1"
            override val chapterCode = "1"
            override val ecCode = "1"
            override val uniqueId: String
                get() = "2"
        },
        object : BaseTransaction() {
            override fun getHeaderString(): String = "1234567890"
            override fun getValueTitle(): String = "Công ty TNHH ABC"
            override fun getValueTaxDescription(): String = "Nộp tdhuế doanh nghiệp hàng tháng"
            override fun getValueAmount(): String = "1,000,000,000 VND"
            override fun getValueCcy(): String = "VND"
            override fun getValueDate(): Pair<String, String> = "dd/MM/yyyy" to "12/12/2024"
            override val amount = "1000000000"
            override val ccy = "VND"
            override val declarationDate = "12/12/2024"
            override val declarationNo = "1234567890"
            override val transDesc = "Nộp thuế doaddnh nghiệp hàng tháng"
            override val taxTypeCode = "1"
            override val ccCode = "VND"
            override val eiTypeCode = "1"
            override val chapterCode = "1"
            override val ecCode = "1"
            override val uniqueId: String
                get() = "3"
        },
        object : BaseTransaction() {
            override fun getHeaderString(): String = "1234567890"
            override fun getValueTitle(): String = "Công ty TNHH ABC"
            override fun getValueTaxDescription(): String = "Nộp thuế doanh nghiệp hàng tháng"
            override fun getValueAmount(): String = "1,000,000,000 VND"
            override fun getValueCcy(): String = "VND"
            override fun getValueDate(): Pair<String, String> = "dd/MM/yyyy" to "12/12/2024"
            override val amount = "1000000000"
            override val ccy = "VND"
            override val declarationDate = "12/12/2024"
            override val declarationNo = "1234567890"
            override val transDesc = "Nộp thuế doanh nghiệp hàng tháng"
            override val taxTypeCode = "1"
            override val ccCode = "VND"
            override val eiTypeCode = "1"
            override val chapterCode = "1"
            override val ecCode = "1"
            override val uniqueId: String
                get() = "4"
        },
    )
}