package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme.colorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.datasource.LoremIpsum
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.card.IBankCardDebit
import vn.com.bidv.designsystem.component.card.IBankCardInit
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardHeaderCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseScreen
import vn.com.bidv.feature.government.service.R
import vn.com.bidv.feature.government.service.model.TaxPayerInfoField
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1.TaxPaymentInfoContent
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun TaxPaymentInfoScreen(
    navHostController: NavHostController,
    progressBar: @Composable () -> Unit,
) {
    val viewModel: TaxPaymentInfoViewModel = hiltViewModel()
    BaseScreen(
        viewModel = viewModel,
        navController = navHostController,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(vn.com.bidv.localization.R.string.chi_tiet_thanh_toan),
            showHomeIcon = true
        )
    ) { uiState, onEvent ->
        Box {
            Box(Modifier.align(Alignment.TopCenter)) {
                progressBar()
            }
            IBankActionBar(
                modifier = Modifier.align(Alignment.BottomCenter),
                buttonPositive = DialogButtonInfo(stringResource(vn.com.bidv.localization.R.string.tiep_tuc)),
                isVertical = false,
                leadingIcon = ImageVector.vectorResource(vn.com.bidv.designsystem.R.drawable.khoan_phai_thu),
                title = stringResource(vn.com.bidv.localization.R.string.tong_tien),
                description = totalAmount.formatMoney("VND", true),
            )
            TaxPaymentInfoContent(uiState, onEvent)
        }
    }
}

@Composable
private fun TaxPaymentInfoContent(
    uiState: TaxPaymentInfoUiState,
    onEvent: (TaxPaymentInfoViewEvent) -> Unit,
) {
    val onTextChange = { field: TaxPaymentFormField, value: String ->
        onEvent(TaxPaymentInfoViewEvent.UpdateFormField(field, value))
    }

    Column(Modifier.verticalScroll(rememberScrollState())) {
        IBankCardInit(
            title = "***********",
            tag = {
                IBankBadgeLabel(
                    badgeColor = LabelColor.ON_BRAND,
                    badgeSize = LabelSize.SM,
                    badgeType = LabelType.ROUNDED,
                    title = "Mặc định"
                )
            },
            content = "2,049,923,745 VND",
        )
        IBankDataCard(
            showCheckbox = false,
            isChecked = true,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(vn.com.bidv.localization.R.string.thong_tin_thu_huong),
                    thumbContent = {}
                )
            },
            cardFooter = {},
            cardContent = {
                Column {
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(vn.com.bidv.localization.R.string.ma_va_ten_kho_bac),
                        text = "",
                    ) {
                        onTextChange(TaxPaymentFormField.TREASURY_CODE, it.text)
                    }
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(vn.com.bidv.localization.R.string.co_quan_thu),
                        text = viewState.taxPayerInfo.taxId,
                    ) {
                        onTextChange(TaxPaymentFormField.REV_AUTH_CODE, it.text)
                    }
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(vn.com.bidv.localization.R.string.tai_khoan_thu_nsnn),
                        text = viewState.taxPayerInfo.taxId,
                    ) {
                        onTextChange(TaxPaymentFormField.REV_ACC_CODE, it.text)
                    }
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(vn.com.bidv.localization.R.string.dia_ban_hanh_chinh),
                        text = viewState.taxPayerInfo.taxId,
                    ) {
                        onTextChange(TaxPaymentFormField.ADM_AREA_CODE, it.text)
                    }
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(vn.com.bidv.localization.R.string.loai_hinh_nnt),
                        text = viewState.taxPayerInfo.taxId,
                    ) {
                        onTextChange(TaxPayerInfoField.TAX_ID, it.text)
                    }
                }
            },
            onCheckedChange = {}
        )

        IBankDataCard(
            showCheckbox = false,
            isChecked = true,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(vn.com.bidv.localization.R.string.thong_tin_khoan_nop),
                    thumbContent = {}
                )
            },
            cardFooter = {},
            cardContent = {
                ListTransactionBaseScreen(

                ) {
                    ItemCardCommon()
                }
            },
            onCheckedChange = {}
        )

        IBankDataCard(
            showCheckbox = false,
            isChecked = true,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(vn.com.bidv.localization.R.string.khac),
                    thumbContent = {}
                )
            },
            cardFooter = {},
            cardContent = {
                IBankInputFieldBase(
                    placeholderText = "Ghi chú tới người duyệt",
                    text = LoremIpsum(10).values.first()
                ) {

                }
            },
            onCheckedChange = {}
        )
    }
}

@Preview
@Composable
fun PreviewTaxPaymentInfoContent() {
    TaxPaymentInfoContent(
        uiState = TaxPaymentInfoUiState(),
        onEvent = {}
    )
}