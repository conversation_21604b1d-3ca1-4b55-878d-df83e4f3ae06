package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class TaxPaymentInfoReducer: Reducer<TaxPaymentInfoUiState, TaxPaymentInfoViewEvent, TaxPaymentInfoSideEffect> {
    override fun reduce(
        previousState: TaxPaymentInfoUiState,
        event: TaxPaymentInfoViewEvent
    ): Pair<TaxPaymentInfoUiState, TaxPaymentInfoSideEffect?> {
        TODO("Not yet implemented")
    }
}

data class TaxPaymentInfoUiState(
    val isInitialized: Boolean = false
): ViewState

enum class TaxPaymentFormField {
    TREASURY_CODE, REV_AUTH_CODE, REV_ACC_CODE, ADM_AREA_CODE,
}

sealed interface TaxPaymentInfoViewEvent: ViewEvent {
    data object InitScreen: TaxPaymentInfoViewEvent
    data class UpdateFormField(val field: TaxPaymentFormField, val value: String): TaxPaymentInfoViewEvent
}

sealed interface TaxPaymentInfoSideEffect: SideEffect {
    data object InitScreen: TaxPaymentInfoSideEffect
}