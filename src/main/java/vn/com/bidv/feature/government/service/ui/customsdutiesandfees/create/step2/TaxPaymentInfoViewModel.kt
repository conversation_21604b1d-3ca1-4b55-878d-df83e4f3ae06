package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.government.service.domain.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TaxPaymentInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase
): ViewModelIBankBase<
        TaxPaymentInfoUiState,
        TaxPaymentInfoViewEvent,
        TaxPaymentInfoSideEffect
        >(
    initialState = TaxPaymentInfoUiState(),
    reducer = TaxPaymentInfoReducer()
) {
    override fun handleEffect(
        sideEffect: TaxPaymentInfoSideEffect,
        onResult: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPaymentInfoSideEffect.InitScreen -> {
                initializeScreenData(onResult)
            }
        }
    }

    private fun initializeScreenData(onResult: (TaxPaymentInfoViewEvent) -> Unit) {
        // Load all dropdown data independently
        var treasuryList: List<TreasuryDMO> = emptyList()
        var revenueAuthorityList: List<RevenueAuthorityDMO> = emptyList()
        var revenueAccountList: List<RevenueAccountDMO> = emptyList()
        var administrativeAreaList: List<AdministrativeAreaDMO> = emptyList()
        var completedOperations = 0
        val totalOperations = 4

        fun checkCompletion() {
            completedOperations++
            if (completedOperations >= totalOperations) {
                onResult(
                    TaxPaymentInfoViewEvent.InitScreenSuccess(
                        treasuryList = treasuryList,
                        revenueAuthorityList = revenueAuthorityList,
                        revenueAccountList = revenueAccountList,
                        administrativeAreaList = administrativeAreaList
                    )
                )
            }
        }

        // Load Treasury list
        callDomain(
            onSuccess = {
                treasuryList = it.data?.items ?: emptyList()
                checkCompletion()
            },
            onFail = {
                treasuryList = emptyList()
                checkCompletion()
            }
        ) {
            governmentServiceUseCase.getListTreasury()
        }

        // Load Revenue Account list
        callDomain(
            onSuccess = {
                revenueAccountList = it.data?.items ?: emptyList()
                checkCompletion()
            },
            onFail = {
                revenueAccountList = emptyList()
                checkCompletion()
            }
        ) {
            governmentServiceUseCase.getListRevenueAccount()
        }

        // Load Revenue Authority list (requires treasury code, using empty string for now)
        callDomain(
            onSuccess = {
                revenueAuthorityList = it.data?.items ?: emptyList()
                checkCompletion()
            },
            onFail = {
                revenueAuthorityList = emptyList()
                checkCompletion()
            }
        ) {
            governmentServiceUseCase.getListRevenueAuthority("")
        }

        // Load Administrative Area list (optional treasury code)
        callDomain(
            onSuccess = {
                administrativeAreaList = it.data?.items ?: emptyList()
                checkCompletion()
            },
            onFail = {
                administrativeAreaList = emptyList()
                checkCompletion()
            }
        ) {
            governmentServiceUseCase.getListAdministrativeArea(null)
        }
    }
}