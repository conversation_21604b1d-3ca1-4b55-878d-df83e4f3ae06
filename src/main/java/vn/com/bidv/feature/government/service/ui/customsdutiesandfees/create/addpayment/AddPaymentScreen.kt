package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentDatePicker
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentViewModel
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.ShowDatePickerBottomSheet
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.EconomicCodeDropDown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.ShowEconomicCodeBottomSheet
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.ShowTaxTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.ShowTradeTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.TaxTypeDropDown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown.TradeTypeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ChapterCodeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.CurrencyTypeDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.CustomsCurrencyDropDown
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowChapterCodeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowCurrencyTypeBottomSheet
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown.ShowCustomsCurrencyBottomSheet

@Composable
fun AddPaymentScreen(
    navController: NavHostController
) {
    val listAddPaymentItems: List<AddPaymentItem> = listOf(
        AddPaymentItem.DeclarationNumber,
        AddPaymentItem.Date,
        AddPaymentItem.DropDownItem.ChapterCode,
        AddPaymentItem.DropDownItem.EconomicCode,
        AddPaymentItem.DropDownItem.CurrencyType,
        AddPaymentItem.InputMoney,
        AddPaymentItem.TransactionDescription,
        AddPaymentItem.DropDownItem.TaxType,
        AddPaymentItem.DropDownItem.CustomsCurrency,
        AddPaymentItem.DropDownItem.TradeType,
    )

    val viewModel: AddPaymentViewModel = hiltViewModel()
    val colorScheme = LocalColorScheme.current

    BaseScreen(
        navController = navController, viewModel = viewModel, topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(vn.com.bidv.localization.R.string.them_khoan_nop),
            showHomeIcon = false
        ), renderContent = { uiState, onEvent ->

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(colorScheme.bgMainPrimary)
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM,
                            bottom = IBSpacing.spacingM
                        )
                ) {
                    AddPaymentScreenContent(
                        uiState = uiState,
                        onEvent = onEvent,
                        listAddPaymentItems = listAddPaymentItems,
                        onValueChange = { field, value ->
                            onEvent(
                                AddPaymentReducer.AddPaymentEvent.ValueTextChange(field, value)
                            )
                        })
                }

                IBankNormalButton(
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .background(LocalColorScheme.current.bgMainTertiary)
                        .padding(16.dp),
                    text = stringResource(vn.com.bidv.localization.R.string.luu)
                ) {
                    onEvent(AddPaymentReducer.AddPaymentEvent.ValidateField)
                }

            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is AddPaymentReducer.AddPaymentEffect.NavigateBack -> {
                    navController.popBackStack()
                }

                else -> {
                    /*nothing*/
                }
            }
        })
}

@Composable
fun AddPaymentScreenContent(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    listAddPaymentItems: List<AddPaymentItem>,
    onValueChange: (field: AddPaymentItem, value: Any?) -> Unit = { _, _ -> },
) {

    val showDatePicker = remember { mutableStateOf(false) }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
    ) {
        items(listAddPaymentItems.size) { index ->
            when (listAddPaymentItems[index]) {
                is AddPaymentItem.DeclarationNumber -> DeclarationNumberInputField(
                    uiState = uiState, onValueChange = onValueChange
                )

                is AddPaymentItem.InputMoney -> AmountInputField(
                    uiState = uiState, onValueChange = onValueChange
                )

                is AddPaymentItem.TransactionDescription -> TransactionDescriptionInputField(
                    uiState = uiState, onValueChange = onValueChange
                )

                is AddPaymentItem.Date -> AddPaymentDatePicker(
                    uiState = uiState, onEvent = onEvent, showDatePicker = showDatePicker
                )

                is AddPaymentItem.DropDownItem.ChapterCode -> ChapterCodeDropDown(
                    uiState = uiState,
                    onEvent = onEvent,
                )

                is AddPaymentItem.DropDownItem.EconomicCode -> EconomicCodeDropDown(
                    uiState = uiState,
                    onEvent = onEvent,
                )

                is AddPaymentItem.DropDownItem.CurrencyType -> CurrencyTypeDropDown(
                    uiState = uiState, onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.TaxType -> TaxTypeDropDown(
                    uiState = uiState, onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.CustomsCurrency -> CustomsCurrencyDropDown(
                    uiState = uiState, onEvent = onEvent
                )

                is AddPaymentItem.DropDownItem.TradeType -> TradeTypeDropDown(
                    uiState = uiState, onEvent = onEvent
                )
            }
        }
    }

    ShowDatePickerBottomSheet(
        showDatePicker = showDatePicker,
        onValueChange = onValueChange
    )

    ShowChapterCodeBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    ShowEconomicCodeBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    ShowCurrencyTypeBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    ShowTaxTypeBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    ShowCustomsCurrencyBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    ShowTradeTypeBottomSheet(
        uiState = uiState,
        onEvent = onEvent,
        onValueChange = onValueChange
    )

    uiState.modalConfirmConfig?.let {
        IBankModalConfirm(
            modalConfirmType = uiState.modalConfirmConfig.modalConfirmType,
            title = uiState.modalConfirmConfig.title,
            supportingText = uiState.modalConfirmConfig.supportingText,
            listDialogButtonInfo = listOf(DialogButtonInfo(label = stringResource(vn.com.bidv.localization.R.string.dong))),
            onDismissRequest = {
                onEvent(AddPaymentReducer.AddPaymentEvent.ShowError(modalConfirm = null))
            })
    }
}
