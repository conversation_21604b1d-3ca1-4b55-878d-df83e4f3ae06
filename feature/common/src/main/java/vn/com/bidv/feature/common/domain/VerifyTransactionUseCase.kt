package vn.com.bidv.feature.common.domain

import com.google.gson.Gson
import com.warrenstrange.googleauth.GoogleAuthenticator
import com.warrenstrange.googleauth.GoogleAuthenticatorConfig
import com.warrenstrange.googleauth.HmacHashFunction
import org.apache.commons.codec.binary.Base32
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.feature.common.domain.data.QrCodeParseDMO
import vn.com.bidv.feature.common.domain.data.TransRetrieveOtpDMO
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPKey
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.retrofit.NetworkResponse
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.mapTo
import java.security.Provider
import java.security.Security
import java.util.Date
import javax.inject.Inject

class VerifyTransactionUseCase @Inject constructor(
    private val verifyTransactionRepository: UtilitiesRepository,
    private val localRepository: LocalRepository,
    private val smartOTPUseCase: SmartOTPUseCase,
) {
    fun genListOTPCode(authId: String, secretKey: String): DomainResult<List<String>> {
        val listOtp = mutableListOf<String>()
        val newSecretKey =
            Base32().encodeToString((secretKey + authId).toByteArray())
        val secureRandomProviders: Array<Provider> = Security.getProviders("SecureRandom.SHA1PRNG")
        val name: String = secureRandomProviders[0].name
        System.setProperty("com.warrenstrange.googleauth.rng.algorithmProvider", name)
        val config = GoogleAuthenticatorConfig.GoogleAuthenticatorConfigBuilder()
            .setSecretBits(256)
            .setHmacHashFunction(HmacHashFunction.HmacSHA256)
            .build()
        val googleAuthenticator = GoogleAuthenticator(config)
        val timeDifference = Storage.get(SmartOTPKey.SYNC_TIME)?.toLong() ?: 0
        var timeGenOtp = Date().time + timeDifference
        for (i in 0..<5) {
            val otp = googleAuthenticator.getTotpPassword(newSecretKey, timeGenOtp).toString()
            if (otp.length < 6) {
                listOtp.add(otp.padStart(6, '0'))
            } else {
                listOtp.add(otp)
            }
            timeGenOtp += Constants.TIME_ACTIVE_OTP
        }
        return DomainResult.Success(listOtp)
    }

    suspend fun shareDataVerifyOTPTransaction(otp: String) {
        localRepository.shareDataTo(
            Constants.VERIFY_OTP, ShareDataDTO(
                Constants.VERIFY_OTP,
                Gson().toJson(VerifyOtpResult(data = otp))
            )
        )
    }

    suspend fun shareDataVerifyPin(data: UserActiveSmartOtpDMO?) {
        val jsonString = Gson().toJson(data)
        localRepository.shareDataTo(
            Constants.VERIFY_OTP, ShareDataDTO(
                Constants.VERIFY_OTP,
                Gson().toJson(
                    VerifyOtpResult(data = jsonString)
                )
            )
        )
    }

    suspend fun lock(userId: String) {
        val currentUser = smartOTPUseCase.getListUserSmartOtpDMO().find { it.userId == userId }
        val smToken = currentUser?.smToken

        if (smToken != null) {
            verifyTransactionRepository.lock(userId, smToken)
        }
    }

    suspend fun selfLock(){
        verifyTransactionRepository.selfLock()
    }

    suspend fun shareDataVerifyTransactionExpired() {
        localRepository.shareDataTo(
            Constants.VERIFY_OTP, ShareDataDTO(
                Constants.VERIFY_OTP,
                Gson().toJson(VerifyOtpResult(errorCode = Constants.TRANSACTION_EXPIRED))
            )
        )
    }

    suspend fun shareDataPopVerifyTransaction() {
        localRepository.shareDataTo(
            Constants.VERIFY_OTP, ShareDataDTO(
                Constants.VERIFY_OTP,
                Gson().toJson(VerifyOtpResult(errorCode = Constants.POP_VERIFY_TRANSACTION))
            )
        )
    }

    suspend fun retrieveOtp(authId: String): DomainResult<TransRetrieveOtpDMO> {
        val result = verifyTransactionRepository.retrieveOtp(authId)
        val transRetrieveOtpDMO = result.convert(TransRetrieveOtpDMO::class.java)
        return transRetrieveOtpDMO
    }

    suspend fun parseQr(data: String): DomainResult<QrCodeParseDMO> {
        val result = verifyTransactionRepository.parseQr(data)
        val qrCodeParseDMO = result.convert(QrCodeParseDMO::class.java)
        qrCodeParseDMO.getSafeData()?.let {
            return DomainResult.Success(it)
        }
        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = result.errorCode,
                errorMessage = result.errorMessage
            )
        }
        return DomainResult.Error(
            errorCode = Constants.PARSE_QR_ERROR,
        )
    }

    suspend fun pushOtp(authId: String, otp: String, smToken: String): DomainResult<Boolean> {
        val result = verifyTransactionRepository.pushOtp(authId, otp, smToken)
        val domain = result.convert {
            this.mapTo(NetworkResponse::class.java).isSuccess()
        }
        return domain
    }

    fun isLoginSuccess() = localRepository.isLoginSuccess()

    suspend fun deleteUserSmartOtp(userId: Long) {
        verifyTransactionRepository.deleteUserSmartOtp(userId = userId)
    }

}